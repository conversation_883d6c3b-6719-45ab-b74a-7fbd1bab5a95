// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import { HTTP_REQUEST_URL, HEADER, TOKENNAME } from '@/config/app';
import { toLogin, checkLogin } from '@/libs/login';
import store from '@/store';
import Cache from '@/utils/cache';

/**
 * 发送请求
 */
function baseRequest(url, method, data, {
	noAuth = true,
	noVerify = false
}) {
	// Get the HTTP_REQUEST_URL with a fallback
	let Url = HTTP_REQUEST_URL, header = HEADER || {'content-type': 'application/json'};

	// Log the URL being used to help with debugging
	//console.log('Using API URL:', Url);

	if (!Url) {
		//console.error('HTTP_REQUEST_URL is not defined');
		return Promise.reject({ msg: 'API URL is not configured properly' });
	}

	if (!noAuth) {
		//登录过期自动登录
		if (!store.state.app.token && !checkLogin()) {
			//toLogin();
			store.commit('LOGIN', {token: false, time: 0});
			return Promise.reject({ msg: `未登录`});
		}
	}
	if (store.state.app.token) header[TOKENNAME] = 'Bearer ' + store.state.app.token;

	return new Promise((reslove, reject) => {
		if (uni.getStorageSync('locale')) {
			header['Cb-lang'] = uni.getStorageSync('locale')
		}
		
		// Create the full URL with proper error handling
		let fullUrl;
		try {
			fullUrl = Url + '/' + url;
		} catch (e) {
			console.error('Error constructing URL:', e);
			fullUrl = HTTP_REQUEST_URL + url;
		}
		
		uni.request({
			url: fullUrl,
			method: method || 'GET',
			header: header,
			data: data || {},
			success: (res) => {
				if (noVerify){
					//reslove(res.data, res);
					reslove(res.data);
				}else if (res.data.status == 200){
					reslove(res.data);
					//reslove(res.data, res);
				}else if (res.data.status == 60001){
					reject(res.data);
				}else if ([100000, 110002, 110003, 110004,100029].indexOf(res.data.status) !== -1) {
					toLogin();
					reject(res.data);
				} else if (res.data.status == 100103) {
					uni.showModal({
						title: `提示`,
						content: res.data.msg || `系统错误`,
						showCancel: false,
						confirmText: `我知道了`
					});
				} else {
					reject(res.data.msg || `系统错误`);
				}
			},
			fail: (msg) => {
				console.error('API request failed:', url, msg);
				let data = {
					mag: `请求失败`,
					status: 1 //1没网
				}
				// #ifdef APP-PLUS
				reject(data);
				// #endif
				// #ifndef APP-PLUS
				reject(`请求失败`);
				// #endif
			}
		})
	});
}

const request = {};

['options', 'get', 'post', 'put', 'head', 'delete', 'trace', 'connect'].forEach((method) => {
	request[method] = (api, data, opt) => baseRequest(api, method, data, opt || {})
});
export default request;
