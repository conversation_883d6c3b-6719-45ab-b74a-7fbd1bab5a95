/**
 * Excel文件解析工具类
 * 用于前端解析Excel文件并提取学员数据
 */

class ExcelParser {
  constructor() {
    this.isXlsxAvailable = false;
    this.checkXlsxAvailability();
  }

  /**
   * 检查xlsx.js库是否可用
   */
  checkXlsxAvailability() {
    this.isXlsxAvailable = typeof XLSX !== 'undefined';
    return this.isXlsxAvailable;
  }

  /**
   * 动态加载xlsx.js库
   */
  loadXlsxLibrary() {
    return new Promise((resolve, reject) => {
      if (this.isXlsxAvailable) {
        resolve();
        return;
      }

      // 创建script标签加载xlsx.js
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
      script.onload = () => {
        this.isXlsxAvailable = typeof XLSX !== 'undefined';
        resolve();
      };
      script.onerror = () => {
        reject(new Error('无法加载xlsx.js库'));
      };
      document.head.appendChild(script);
    });
  }

  /**
   * 解析Excel文件
   * @param {string} filePath - 文件路径
   * @param {Function} callback - 解析完成回调
   */
  parseExcelFile(filePath, callback) {
    const fs = uni.getFileSystemManager();
    
    fs.readFile({
      filePath: filePath,
      encoding: 'binary',
      success: (res) => {
        if (this.isXlsxAvailable) {
          this.parseWithXLSX(res.data, callback);
        } else {
          this.parseAsCSV(filePath, callback);
        }
      },
      fail: (err) => {
        callback(new Error('读取文件失败: ' + err.errMsg), null);
      }
    });
  }

  /**
   * 使用xlsx.js解析Excel
   * @param {string} binaryData - 二进制数据
   * @param {Function} callback - 解析完成回调
   */
  parseWithXLSX(binaryData, callback) {
    try {
      const workbook = XLSX.read(binaryData, { type: 'binary' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      // 转换为JSON数组
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      // 提取学员数据（跳过标题行）
      const studentList = [];
      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (row && row.length >= 2) {
          const name = String(row[0] || '').trim();
          const phone = String(row[1] || '').trim();
          
          if (name && phone) {
            studentList.push({
              name: name,
              phone: phone
            });
          }
        }
      }
      
      callback(null, studentList);
    } catch (e) {
      callback(new Error('Excel解析失败: ' + e.message), null);
    }
  }

  /**
   * 简化的CSV解析（备选方案）
   * @param {string} filePath - 文件路径
   * @param {Function} callback - 解析完成回调
   */
  parseAsCSV(filePath, callback) {
    const fs = uni.getFileSystemManager();
    
    fs.readFile({
      filePath: filePath,
      encoding: 'utf8',
      success: (res) => {
        try {
          const text = res.data;
          const lines = text.split('\n').filter(line => line.trim());
          
          // 跳过标题行，从第二行开始
          const studentList = [];
          for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line) {
              // 支持逗号、制表符、分号分隔
              const parts = line.split(/[,\t;]/);
              if (parts.length >= 2) {
                const name = parts[0].trim();
                const phone = parts[1].trim();
                
                if (name && phone) {
                  studentList.push({
                    name: name,
                    phone: phone
                  });
                }
              }
            }
          }
          
          callback(null, studentList);
        } catch (e) {
          callback(new Error('CSV解析失败: ' + e.message), null);
        }
      },
      fail: (err) => {
        callback(new Error('读取文件失败: ' + err.errMsg), null);
      }
    });
  }

  /**
   * 验证学员数据格式
   * @param {Array} studentList - 学员数据数组
   * @returns {Object} 验证结果
   */
  validateStudentData(studentList) {
    const validStudents = [];
    const invalidStudents = [];
    
    studentList.forEach((student, index) => {
      const name = student.name ? student.name.trim() : '';
      const phone = student.phone ? student.phone.trim() : '';
      
      if (name && phone && /^1[3-9]\d{9}$/.test(phone)) {
        validStudents.push({
          name: name,
          phone: phone,
          originalIndex: index + 2 // Excel行号从2开始（跳过标题行）
        });
      } else {
        invalidStudents.push({
          name: name,
          phone: phone,
          originalIndex: index + 2,
          error: !name ? '姓名为空' : !phone ? '电话为空' : '电话格式不正确'
        });
      }
    });
    
    return {
      valid: validStudents,
      invalid: invalidStudents,
      total: studentList.length,
      validCount: validStudents.length,
      invalidCount: invalidStudents.length
    };
  }
}

// 创建全局实例
window.ExcelParser = new ExcelParser();

export default ExcelParser;