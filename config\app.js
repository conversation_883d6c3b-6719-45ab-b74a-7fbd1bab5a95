module.exports = {
	// 小程序配置
	// #ifdef MP || APP-PLUS
	// 请求域名 格式： https://您的域名
	HTTP_REQUEST_URL: `https://app.jksq.top`,
	//HTTP_REQUEST_URL: `https://devapp.jksq.top`,
	// #endif

	// H5配置
	// #ifdef H5
	//H5接口是浏览器地址，非单独部署不用修改
	HTTP_REQUEST_URL: (function() {
		try {
			return window.location.protocol + "//" + window.location.host;
		} catch (e) {
			console.error('Window object not available, using fallback URL');
			return 'https://app.jksq.top'; // Fallback to the same URL as MP config
		}
	})(),
	// #endif 

	// 通用备用URL，如果以上都不满足条件，默认使用这个
	HTTP_REQUEST_URL: `https://app.jksq.top`,
	
	// 后台版本号
	SYSTEM_VERSION: '001',
	
	// 以下配置在不做二开的前提下,不需要做任何的修改
	HEADER: {
		'content-type': 'application/json',
		//#ifdef H5
		'Form-type': (function() {
			try {
				return navigator.userAgent.toLowerCase().indexOf("micromessenger") !== -1 ? 'wechat' : 'h5';
			} catch (e) {
				return 'h5';
			}
		})(),
		//#endif
		//#ifdef MP
		'Form-type': 'routine',
		//#endif
		//#ifdef APP-VUE
		'Form-type': 'app',
		//#endif
	},
	// 回话密钥名称 请勿修改此配置
	TOKENNAME: 'Auth-MrInte',
	// 缓存时间 0 永久
	EXPIRE: 0,
	//分页最多显示条数
	LIMIT: 10
}
