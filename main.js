import Vue from 'vue'
import App from './App'
import store from './store'
import Cache from './utils/cache'
import util from 'utils/util'
import configs from './config/app.js'
Vue.prototype.$util = util;
Vue.prototype.$config = configs;
Vue.prototype.$Cache = Cache;
Vue.prototype.$eventHub = new Vue();
Vue.config.productionTip = false

// Global error handler
Vue.config.errorHandler = function(err, vm, info) {
	console.error('Vue global error:', err);
	console.info('Error component:', vm);
	console.info('Error info:', info);
};

App.mpType = 'app'

const app = new Vue({
	...App,
	store,
	Cache
})
app.$mount();
