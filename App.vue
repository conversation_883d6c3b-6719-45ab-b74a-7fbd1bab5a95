<script>
	import {mapGetters} from "vuex"
	import colors from '@/mixins/color.js';
	import Cache from '@/utils/cache';
	
	//import {checkLogin} from '@/libs/login';
	import {HTTP_REQUEST_URL} from '@/config/app';
	import {basicConfig} from '@/api/public';
	//import Auth from '@/libs/wechat.js';
	import Routine from '@/libs/routine.js';
	
	import { silenceBindingSpread } from "@/utils";
	export default {
		globalData: {
			spid: 0,
			shareType: 'coach',
			code: 0,
			isLogin: false,
			userInfo: {},
			MyMenus: [],
			globalData: false,
			isIframe: false,
			tabbarShow: true,
			windowHeight: 0,
			locale: ''
		},
		mixins: [colors],
		computed: mapGetters(['isLogin']),
		watch: {
			isLogin: {
				deep: true, //深度监听设置为 true
				handler: function(newV, oldV) {
					if (newV) {
						// this.getCartNum()
					} else {
						//this.$store.commit('indexData/setCartNum', '')
					}
				}
			}
		},
		async onLaunch() {
			var that = this;
			try {
				//console.log('Starting onLaunch hook');
				
				// Define fallback configuration
				let fallbackConfig = {
					wechat_status: false, 
					wechat_auth_switch: false,
					version: '1.0.0'
				};
				
				try {
					// Wrap the basicConfig call in a try-catch to handle specific API errors
					var config = await basicConfig();
					//console.log('Basic config response:', config);
					
					if(typeof config.data === 'string'){
						try {
							config.data = JSON.parse(config.data);
						} catch (parseError) {
							//console.error('Error parsing config data:', parseError);
							// If parsing fails, use the original string data
						}
					}
					if(config.data.data){
						config.data = config.data.data;
					}
					uni.setStorageSync('BASIC_CONFIG', config.data);
					Cache.set('BASIC_CONFIG', config.data);
				} catch (apiError) {
					console.error('API call failed:', apiError);
					// Use fallback if API call fails
					uni.setStorageSync('BASIC_CONFIG', fallbackConfig);
					Cache.set('BASIC_CONFIG', fallbackConfig);
				}
				
				let blue = '--view-theme: rgba(29,176,252,1);--view-theme-16:#1db0fc;--view-priceColor:#FD502F;--view-minorColor:rgba(58, 139, 236, 0.5);--view-minorColorT:rgba(9, 139, 243, 0.1);--view-bntColor:#22CAFD;--view-op-ten: rgba(29,176,252, 0.1);--view-main-start:#40D1F4; --view-main-over:#1DB0FC;--view-op-point-four: rgba(29,176,252, 0.04);--view-op-point-eight: rgba(29,176,252, 0.8);--view-linear:linear-gradient(180deg, rgba(29,176,252,0.2) 0%, rgba(255,255,255,0) 100%);--view-success-text:#0bb800;--view-success-bg:#00cc66;--view-number-color: #eaff00;'
				uni.setStorageSync('viewColor', blue)
				
				// 获取导航高度；
				uni.getSystemInfo({
					success: function(res) {
						that.globalData.navHeight = res.statusBarHeight * (750 / res.windowWidth) + 91;
					}
				});
				// #ifdef MP
				let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
				that.globalData.navH = menuButtonInfo.top * 2 + menuButtonInfo.height / 2;
				const version = uni.getSystemInfoSync().SDKVersion
				if (Routine.compareVersion(version, '2.21.3') >= 0) {
					that.$Cache.set('MP_VERSION_ISNEW', true)
				} else {
					that.$Cache.set('MP_VERSION_ISNEW', false)
				}
				// #endif
			} catch (error) {
				console.error('Error in onLaunch:', error);
				// Provide fallback configuration for any error
				let fallbackConfig = {
					wechat_status: false, 
					wechat_auth_switch: false,
					version: '1.0.0'
				};
				uni.setStorageSync('BASIC_CONFIG', fallbackConfig);
				Cache.set('BASIC_CONFIG', fallbackConfig);
			}
		},
		onShow: function() {
			const queryData = uni.getEnterOptionsSync() // uni-app版本 3.5.1+ 支持
			if (queryData.query.spid) {
				this.$Cache.set('spid', queryData.query.spid);
				this.globalData.spid = queryData.query.spid;
				silenceBindingSpread(this.globalData)
			}
			if (queryData.query.shareType) {
				this.$Cache.set('shareType', queryData.query.shareType);
				this.globalData.shareType = queryData.query.shareType;
				silenceBindingSpread(this.globalData)
			}else{
				var tempShareType = 'coach';
				this.$Cache.set('shareType', tempShareType);
				this.globalData.shareType = tempShareType;
				silenceBindingSpread(this.globalData)
			}
			// #ifdef MP
			if (queryData.query.scene) {
				let param = this.$util.getUrlParams(decodeURIComponent(queryData.query.scene))
				if (param.spid) {
					this.$Cache.set('spid', param.spid);
					this.globalData.spid = param.spid;
				}
				if (param.shareType) {
					this.$Cache.set('shareType', param.shareType);
					this.globalData.shareType = param.shareType;
				}
				silenceBindingSpread(this.globalData)
			}
			// #endif
		},
		onHide: function() {
			//console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import 'static/css/base.css';
	@import 'static/css/guildford.css';
	@import 'static/css/style.scss';
	@import 'static/iconfont/iconfont.css';

	.wrapper{position: relative;}
	.wrapper .bag {
		position: absolute;top: 0;left: 0;width: 100%;opacity: .8;z-index: -1;
		/* #ifdef H5 */
		z-index: 0;
		/* #endif */
	}
	.wrapper .bag {
		position: absolute;top: 0;left: 0;width: 100%;opacity: .8;z-index: -1;
		/* #ifdef H5 */
		z-index: 0;
		/* #endif */
	}
	.wrapper .bag img {width: 100%;height: 838rpx;}
	.wrapper .system-height{width: 100%;}
	.wrapper .title-bar {
			position: relative;display: flex;align-items: center;justify-content: center;font-size: 34rpx;font-weight: 500;color: #333333;line-height: 48rpx;
	}
	.wrapper .title-bar .icon{position: absolute;left: 30rpx;top: 0;display: flex;align-items: center;justify-content: center;width: 80rpx;height: 80rpx;}
	
	.FooterLine{height: 160rpx;width: 100%;}
	.empty-box{padding-bottom: 140rpx;}
</style>
