<template>
  <view class="batch-add-container" :style="'background-image: url('+(basicConfig && basicConfig.BgImage ? basicConfig.BgImage : '')+');'">
    <view class="batch-add-row">
      <view style="line-height: 2;font-weight: bold;">表格格式：</view>
      <view style="line-height: 2;">
        <view style="font-size: 30rpx;">1、第一列学员姓名</view>
        <view style="font-size: 30rpx;">2、第二列学员电话</view>
        <view style="font-size: 30rpx;">3、第一行为标题</view>
        <view style="font-size: 30rpx;">4、第二行开始为值</view>
      </view>
    </view>
    <view class="batch-add-row">
      <view class="example-title" style="font-weight: bold;">示例如下：</view>
      <image class="example-img" src="@/static/images/excel-example.png" mode="widthFix" />
    </view>
    <button class="upload-btn" @tap="uploadExcel" :disabled="!isApiReady">上传表格</button>
    <!-- <button class="down-btn" @tap="downExcel" :disabled="!isApiReady">下载模板</button> -->
    <button class="close-btn" @tap="close">取消上传</button>
  </view>
</template>

<script>
export default {
  props: {
    basicConfig: {
      type: Object,
      default: () => {}
    },
    apiURL: {
      type: String,
      default: ''
    }
  },
  data(){
    return {
      isApiReady: false,
      studentList: []
    }
  },
  
  mounted() {
    this.checkApiConfig();
  },
  methods: {
    checkApiConfig() {
      this.isApiReady = !!(this.apiURL && this.apiURL.trim());
      if (!this.isApiReady) {
        console.warn('API地址未配置，批量添加功能可能无法正常使用');
      }
    },
    downExcel() {
      if (!this.apiURL) {
        uni.showToast({ title: '配置错误：API地址未设置', icon: 'none' });
        return;
      }
      
      var url = this.apiURL + '/download/file/demoExcel.xlsx';
      uni.showLoading({ title: '下载中...' });
      
      uni.downloadFile({
        url: url,
        success: (res) => {
          uni.hideLoading();
          if (res.statusCode === 200) {
            uni.saveFile({
              tempFilePath: res.tempFilePath,
              success: (saveRes) => {
                uni.showToast({ title: '下载成功', icon: 'success' });
                // 打开文件
                uni.openDocument({
                  filePath: saveRes.savedFilePath,
                  fileType: 'xlsx',
                  showMenu: true,
                  success: function (openRes) {
                    console.log('打开文档成功');
                  },
                  fail: function (openErr) {
                    console.log('打开文档失败:', openErr);
                  }
                });
              },
              fail: (err) => {
                uni.showToast({ title: '保存失败：' + err.errMsg, icon: 'none' });
              }
            });
          } else {
            uni.showToast({ title: '下载失败：服务器错误(' + res.statusCode + ')', icon: 'none' });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({ title: '下载失败：' + err.errMsg, icon: 'none' });
        }
      });
    },
    close() {
      this.$emit('close');
    },
    uploadExcel() {
      // 使用平台兼容的文件选择方法
      const chooseFileMethod = uni.chooseMessageFile || uni.chooseFile;
      
      if (!chooseFileMethod) {
        uni.showToast({ title: '当前平台不支持文件选择', icon: 'none' });
        return;
      }
      
      const fileOptions = {
        count: 1,
        type: 'file',
        extension: ['xls', 'xlsx'],
        success: (chooseRes) => {
          // 兼容不同平台的文件返回格式
          let file;
          if (chooseRes.tempFiles) {
            file = chooseRes.tempFiles[0];
          } else if (chooseRes.tempFilePaths) {
            file = {
              path: chooseRes.tempFilePaths[0],
              name: chooseRes.tempFilePaths[0].split('/').pop(),
              size: 0 // chooseFile不返回文件大小，需要后续处理
            };
          }
          
          if (!file) {
            uni.showToast({ title: '未选择文件', icon: 'none' });
            return;
          }
          
          // 验证文件大小 (限制为10MB)
          const maxSize = 10 * 1024 * 1024;
          if (file.size > maxSize) {
            uni.showToast({ title: '文件过大，请选择小于10MB的文件', icon: 'none' });
            return;
          }
          
          uni.showLoading({ title: '读取文件中...' });
          
          // 读取Excel文件内容
          this.readExcelFile(file);
        },
        fail: (err) => {
          if (err.errMsg.indexOf('cancel') === -1) {
            uni.showToast({ title: '选择文件失败：' + err.errMsg, icon: 'none' });
          }
        }
      };
      
      // 移除type和extension参数（某些平台不支持）
      if (chooseFileMethod === uni.chooseFile) {
        delete fileOptions.type;
        delete fileOptions.extension;
        fileOptions.type = 'all';
      }
      
      chooseFileMethod(fileOptions);
    },
    
    readExcelFile(file) {
      // 使用内置的Excel解析器
      this.parseExcelDirect(file);
    },
    
    parseExcelDirect(file) {
      uni.getFileSystemManager().readFile({
        filePath: file.path,
        encoding: 'binary',
        success: (res) => {
          try {
            // 检查是否支持xlsx.js
            if (typeof XLSX !== 'undefined') {
              this.parseWithXLSX(res.data);
            } else {
              // 使用内置的简化解析
              this.parseWithBuiltInParser(res.data, file);
            }
          } catch (e) {
            uni.hideLoading();
            uni.showModal({
              title: '解析错误',
              content: '文件解析失败，请确保文件格式正确',
              showCancel: false
            });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({ title: '读取文件失败：' + err.errMsg, icon: 'none' });
        }
      });
    },
    
    parseWithXLSX(binaryData) {
      try {
        const workbook = XLSX.read(binaryData, { type: 'binary' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // 转换为JSON数组，header: 1表示返回数组形式
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        // 提取学员数据（跳过标题行，从第二行开始）
        const studentList = [];
        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i];
          if (row && row.length >= 2) {
            const name = String(row[0] || '').trim();
            const phone = String(row[1] || '').trim();
            
            if (name && phone) {
              studentList.push({
                name: name,
                phone: phone
              });
            }
          }
        }
        
        uni.hideLoading();
        
        if (studentList.length === 0) {
          uni.showToast({ title: '未找到有效的学员数据', icon: 'none' });
          return;
        }
        
        this.studentList = studentList;
        this.showDataPreview(studentList);
        
      } catch (e) {
        uni.hideLoading();
        uni.showModal({
          title: '解析错误',
          content: 'Excel文件解析失败：' + e.message,
          showCancel: false
        });
      }
    },
    
    parseWithBuiltInParser(binaryData, file) {
      // 简化的Excel解析器（处理CSV或简单文本格式）
      uni.getFileSystemManager().readFile({
        filePath: file.path,
        encoding: 'utf8',
        success: (res) => {
          try {
            const text = res.data;
            const lines = text.split(/\r?\n/).filter(line => line.trim());
            
            // 跳过标题行，从第二行开始
            const studentList = [];
            for (let i = 1; i < lines.length; i++) {
              const line = lines[i].trim();
              if (line) {
                // 支持逗号、制表符、分号、竖线分隔
                const parts = line.split(/[,\t;|]/);
                if (parts.length >= 2) {
                  const name = parts[0].trim();
                  const phone = parts[1].trim();
                  
                  if (name && phone) {
                    studentList.push({
                      name: name,
                      phone: phone
                    });
                  }
                }
              }
            }
            
            uni.hideLoading();
            
            if (studentList.length === 0) {
              uni.showToast({ title: '未找到有效的学员数据', icon: 'none' });
              return;
            }
            
            this.studentList = studentList;
            this.showDataPreview(studentList);
            
          } catch (e) {
            uni.hideLoading();
            uni.showModal({
              title: '解析错误',
              content: '文件内容格式不正确',
              showCancel: false
            });
          }
        },
        fail: () => {
          uni.hideLoading();
          this.uploadExcelFile(file); // 回退到文件上传
        }
      });
    },
    
    showDataPreview(studentList) {
      // 显示数据预览
      const previewText = studentList.slice(0, 3).map(item => 
        `${item.name} - ${item.phone}`
      ).join('\n');
      
      const moreCount = studentList.length > 3 ? studentList.length - 3 : 0;
      const message = `共读取到 ${studentList.length} 条学员数据` +
        (moreCount > 0 ? `，预览前3条：\n${previewText}\n...等${moreCount}条` : `：\n${previewText}`);
      
      uni.showModal({
        title: '数据预览',
        content: message,
        success: (res) => {
          if (res.confirm) {
            this.processStudentData(studentList);
          }
        }
      });
    },
    
    processStudentData(studentList) {
      if (!studentList || studentList.length === 0) {
        uni.showToast({ title: '未找到有效的学员数据', icon: 'none' });
        return;
      }
      
      // 验证数据格式
      const validStudents = studentList.filter(item => 
        item.name && item.phone && 
        item.name.trim() && item.phone.trim() &&
        /^1[3-9]\d{9}$/.test(item.phone.trim())
      );
      
      if (validStudents.length === 0) {
        uni.showModal({
          title: '数据验证失败',
          content: '未找到有效的学员数据，请确保：\n1. 第一列是学员姓名\n2. 第二列是学员电话\n3. 电话格式正确',
          showCancel: false
        });
        return;
      }
      
      if (validStudents.length !== studentList.length) {
        uni.showModal({
          title: '数据验证提醒',
          content: `共找到${studentList.length}条数据，其中${validStudents.length}条有效，${studentList.length - validStudents.length}条数据格式不正确，是否继续？`,
          success: (res) => {
            if (res.confirm) {
              this.uploadStudentData(validStudents);
            }
          }
        });
      } else {
        this.uploadStudentData(validStudents);
      }
    },
    
    uploadStudentData(studentList) {
      if (!this.apiURL) {
        uni.showToast({ title: '配置错误：API地址未设置', icon: 'none' });
        return;
      }
      
      uni.showLoading({ title: '上传学员数据...' });
      
      uni.request({
        url: this.apiURL + '/student/batchAdd',
        method: 'POST',
        data: {
          students: studentList
        },
        success: (res) => {
          uni.hideLoading();
          
          if (res.data.code === 200) {
            uni.showToast({ 
              title: '上传成功：共导入' + (res.data.data || studentList.length) + '条学员数据', 
              icon: 'success',
              duration: 2000
            });
            this.$emit('uploadSuccess', res.data.data || studentList.length);
          } else {
            uni.showModal({
              title: '上传失败',
              content: res.data.message || '服务器处理失败',
              showCancel: false
            });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({ title: '上传失败：' + err.errMsg, icon: 'none' });
        }
      });
    },
    
    uploadExcelFile(file) {
      // 原始的文件上传方式（保留作为备选）
      if (!this.apiURL) {
        uni.showToast({ title: '配置错误：API地址未设置', icon: 'none' });
        return;
      }
      
      uni.showLoading({ title: '上传中...' });
      
      uni.uploadFile({
        url: this.apiURL + '/upload/excel/batchAdd',
        filePath: file.path,
        name: 'file',
        formData: {
          'filename': file.name
        },
        success: (uploadRes) => {
          uni.hideLoading();
          
          try {
            const data = JSON.parse(uploadRes.data);
            if (data.code === 200) {
              uni.showToast({ 
                title: '上传成功：共导入' + (data.data || 0) + '条数据', 
                icon: 'success',
                duration: 2000
              });
              this.$emit('uploadSuccess', data.data);
            } else {
              uni.showModal({
                title: '上传失败',
                content: data.message || '服务器处理失败',
                showCancel: false
              });
            }
          } catch (e) {
            uni.showModal({
              title: '解析错误',
              content: '服务器返回数据格式错误',
              showCancel: false
            });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({ title: '上传失败：' + err.errMsg, icon: 'none' });
        }
      });
    }
  }
}
</script>

<style scoped>
.batch-add-container {
  background: rgba(0, 0, 0, 0.6);
  padding: 32px;
  border-radius: 12px;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
}
.batch-add-row{
  display: flex;
  flex-direction: row;
  width: 100%;
  font-size: 28rpx;
  gap: 20rpx;
  padding-top: 30rpx;
}
.desc {
  font-size: 18px;
  line-height: 1.8;
  margin-bottom: 12px;
  text-align: left;
  width: 100%;
}
.example-img {
  width: 220px;
  margin-bottom: 24px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  background: #fff;
}
.upload-btn {
  width: 180px;
  height: 44px;
  line-height: 44px;
  background: #098d00;
  color: #fff;
  border-radius: 8px;
  font-size: 14px;
  margin-top: 12px;
}
.upload-btn[disabled] {
  background: #7cb87c;
  color: #e0e0e0;
}
.down-btn {
  width: 180px;
  height: 44px;
  line-height: 44px;
  background: #5c8cfb;
  color: #fff;
  border-radius: 8px;
  font-size: 14px;
  margin-top: 12px;
}
.down-btn[disabled] {
  background: #a0c0ff;
  color: #e0e0e0;
}
.close-btn {
  width: 180px;
  height: 44px;
  line-height: 44px;
  background: #EEE;
  color: #666;
  border-radius: 8px;
  font-size: 14px;
  margin-top: 12px;
}
</style>
