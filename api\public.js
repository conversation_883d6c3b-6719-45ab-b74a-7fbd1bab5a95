// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from "@/utils/request.js";
import wechat from "@/libs/wechat.js";


/**
 * 获取OpenID
 * @param	string	code
 */
export function getOpenIDApi(code){
	return request.get('weixin/openid',{code:code})
}

/**
 * 获取微信sdk配置
 * @returns {*}
 */
export function getWechatConfig() {
	return request.get(
		"wechat/config", {
			url: wechat.signLink()
		}, {
			noAuth: true
		}
	);
}

/**
 * 获取微信sdk配置
 * @returns {*}
 */
export function wechatAuth(code, spread, login_type) {
	return request.get(
		"wechat/auth", {
			code,
			spread,
			login_type
		}, {
			noAuth: true
		}
	);
}

/**
 * 分享
 * @returns {*}
 */
export function getShare() {
	return request.get("share", {}, {
		noAuth: true
	});
}

/**
 * 公众号登录
 * @returns {*}
 */
export function wechatAuthLogin(data) {
	return request.get("v2/wechat/auth_login", data, {
		noAuth: true
	});
}

/**
 * 获取关注海报
 * @returns {*}
 */
export function follow() {
	return request.get("wechat/follow", {}, {
		noAuth: true
	});
}

/**
 * 获取图片base64
 * @retins {*}
 * */
export function imageBase64(image, code) {
	return request.post(
		"image_base64", {
			image: image,
			code: code
		}, {
			noAuth: true
		}
	);
}

/**
 * 自动复制口令功能
 * @returns {*}
 */
export function copyWords() {
	return request.get("copy_words", {}, {
		noAuth: true
	});
}

/**
 * 获取微信sdk配置
 * @returns {*}
 */
export function wechatAuthV2(code, spread) {
	return request.get(
		"v2/wechat/auth", {
			code,
			spread
		}, {
			noAuth: true
		}
	);z
}

/**
 * 配置信息
 * 
 */
export function basicConfig() {
	return request.get("basic_config");
}

/**
 * 后台版本信息
 * 
 */
export function getSystemVersion() {
	return request.get(`version`, {}, {
		noAuth: true
	});
}

/**
 * iframe登录
 * 
 */
export function remoteRegister(data) {
	return request.get(`remote_register`, data, {
		noAuth: true
	});
}


/**
 * 获取考场省市区列表
 */
export function getExamAreaList(city_id) {
	return request.get(`exam/list`,{city_id: city_id});
}