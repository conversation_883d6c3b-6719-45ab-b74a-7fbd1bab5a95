{"_from": "@dcloudio/uni-ui", "_id": "@dcloudio/uni-ui@1.5.7", "_inBundle": false, "_integrity": "sha512-DugxSIrQrze1FLdUOj9a+JEQ0bHGjnJTcGUK1mN/MivKg7nuKJBRWk5Ipa9sUdoBznX6ndz5h2e7Uao6x1CdCw==", "_location": "/@dcloudio/uni-ui", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "@dcloudio/uni-ui", "name": "@dcloudio/uni-ui", "escapedName": "@dcloudio%2funi-ui", "scope": "@dcloudio", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/@dcloudio/uni-ui/-/uni-ui-1.5.7.tgz", "_shasum": "74daef1f537ae2b9320e51f44163fe56bdd19c87", "_spec": "@dcloudio/uni-ui", "_where": "D:\\工作目录\\驾校\\前端\\H5支付", "author": "", "browserslist": ["Android >= 4", "ios >= 8"], "bugs": {"url": "https://github.com/dcloudio/uni-ui/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "## uni-ui产品特点", "devDependencies": {}, "homepage": "https://github.com/dcloudio/uni-ui#readme", "license": "Apache-2.0", "name": "@dcloudio/uni-ui", "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-ui.git"}, "version": "1.5.7"}