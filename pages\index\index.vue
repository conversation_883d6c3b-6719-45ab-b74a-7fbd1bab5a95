<template>
	<view class="pageView" :style="'background-image: url('+(basicConfig && basicConfig.BgImage ? basicConfig.BgImage : '')+');'">
			<view class="PageLogo" v-if="basicConfig && basicConfig.site_logo">
				<image class="logo" :src="(basicConfig.site_logo)" mode="widthFix"/>
				<view class="appName">{{ basicConfig.site_name }}</view>
			</view>

			<view class="bottomView" v-if="basicConfig && basicConfig.company_logo">
				<image :src="basicConfig.company_logo" mode="widthFix"></image>
				<view>ICP备案号：<a href="https://beian.miit.gov.cn/" target="_blank">{{ basicConfig.ICPNO }}</a></view>
			</view>

			<view class="cityView">
				<view class="CityLabel">考场选择：</view>
				<view class="citySelectView">
					<!-- 省市区选择器区域 -->
					<view class="picker-container">
						<view class="picker-item" @tap="showPickerType('province')">
							<image class="pickerLineImg" src="@/static/images/SelectL.png" mode="heightFix"/>
							<view class="pickerLineCenter">
								<text class="picker-text">{{ province.label || '选择省份' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
							<image class="pickerLineImg" src="@/static/images/SelectR.png" mode="heightFix" />
						</view>
						<view class="picker-item" @tap="showPickerType('city')" :class="{ 'disabled': !province }">
							<image class="pickerLineImg" src="@/static/images/SelectL.png" mode="heightFix"/>
							<view class="pickerLineCenter">
								<text class="picker-text">{{ city.label || '选择城市' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
							<image class="pickerLineImg" src="@/static/images/SelectR.png" mode="heightFix"/>
						</view>
						<view class="picker-item" @tap="showPickerType('area')" :class="{ 'disabled': !city }">
							<image class="pickerLineImg" src="@/static/images/SelectL.png" mode="heightFix"/>
							<view class="pickerLineCenter">
								<text class="picker-text">{{ area.label || '选择区域' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
							<image class="pickerLineImg" src="@/static/images/SelectR.png" mode="heightFix"/>
						</view>

						<!-- 考场列表 -->
						<view class="picker-item" @tap="showPickerType('exam')">
							<image class="pickerLineImg" src="@/static/images/SelectL.png" mode="heightFix"/>
							<view class="pickerLineCenter">
								<text class="icker-text">{{ exam.exam_name || '选择考场' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
							<image class="pickerLineImg" src="@/static/images/SelectR.png" mode="heightFix"/>
						</view>
					</view>
				</view>
			</view>

			<view class="cityView" style="padding-top: 0px;">
				<view class="CityLabel">批量添加：</view>
				<view class="citySelectView" style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
					<switch class="switch" :checked="addtype == 2" @change="changeAddType" style="transform:scale(0.8);"></switch>
				</view>
			</view>
			
			<block v-if="addtype == 2">
				<BatchAdd :basicConfig="basicConfig" :apiURL="apiURL" @close="closeBatchAdd()"></BatchAdd>

			</block>

			<block v-if="addtype == 1">
				<view class="cityView">
					<view class="CityLabel">手机号：</view>
					<view class="citySelectView" style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
						<input class="inputPhone" type="tel" maxlength="11" v-model="phone" placeholder="请输入手机号" @input="changePhone"/>
						<button class="buttonPhone" @tap="getExamPrice()">确认</button>
					</view>
				</view>
				<!--
				<view class="cityView" v-if="userInfo.usercoach && userInfo.usercoach.avatar">
					<view class="CityLabel" style="line-height: 80rpx;">教　练：</view>
					<view class="coachInfo">
						<image class="coachAvatar" :src="userInfo.usercoach.avatar" mode="widthFix" />
						<text class="coachNickname">{{ userInfo.usercoach.nickname }}</text>
					</view>
				</view>
				-->

				<view class="cityView" v-if="isNew">
					<view class="CityLabel"></view>
					<view class="citySelectView" style="color:#eaff00;font-size: 12px;">{{ notUserText }}</view>
				</view>
				
				<view class="cityView" v-if="(!userInfo.usercoach || !userInfo.usercoach.avatar) && !userInfo.isCoach && exam.id && !isChangePhone">
					<view class="CityLabel">姓　名：</view>
					<view class="citySelectView" style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
						<input class="inputPhone" type="text" maxlength="20" v-model="nickname" placeholder="请输入学员姓名(选填)"/>
						<button class="buttonPhone" style="opacity: 0;">确认</button>
					</view>
				</view>

				<view class="cityView" v-if="(!userInfo.usercoach || !userInfo.usercoach.avatar) && !userInfo.isCoach && exam.id && !isChangePhone">
					<view class="CityLabel">优惠码：</view>
					<view class="citySelectView" style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
						<input class="inputPhone" type="number" v-model="coachcode" placeholder="请输入优惠码"/>
						<button class="buttonPhone" @tap="bindCoach()">确认</button>
					</view>
				</view>

				<view v-if="cardMember.id" class="buyView" :style="'background-image: url('+(cardMember.h5_price_image)+');height: calc((100vw - 60rpx ) * '+(parseFloat(cardMember.priceImageAspect).toFixed(2))+')'">
					<view class="BuyViewTitle">{{ ExamInfo.exam_name }}（{{ cardMember.title }}）</view>
					<view class="BuyTips">
						<view class="buyTipItem" v-for="(item,index) in cardMember.cardinfo">
							<image :src="item.imageUrl" mode="widthFix" style="max-width: 16rpx;max-height: 16rpx;" />
							<text>{{item.text}}</text>
						</view>
					</view>
					<view class="buyPrice">
						<view class="buyPriceItem">原价：{{ cardMember.price }}元</view>
						<view class="buyPriceItem lushe">特惠价：{{ cardMember.truePrice || (cardMember.coachPrice || cardMember.price) }}元</view>
						<view class="buyPriceItem lushe">{{ cardMember.priceinfo }}</view>

						<view class="buyPriceItem">
							<view class="iconfont icon-gou" style="border: 1rpx solid #1499E8;font-size: 17rpx;margin-right:10rpx;color:#5CC1E6;"></view>
							<view>
								我已阅读并同意<span style="color:#FFBD00;">《会员协议》</span>
							</view>
						</view>
					</view>
				</view>

				<view class="buyBtnView" v-if="cardMember.id">
					<button @tap="buyMember()" v-if="!isBuy">
						<view>{{ cardMember.truePrice || cardMember.price }}元　微信支付</view>
						<image :src="cardMember.set_school_price_pay_image"  mode="widthFix" />
					</button>
					<button class="notCantBuy" v-else>已购买</button>
				</view>
			</block>

		<!-- 省份选择弹窗 -->
		<uni-popup ref="provincePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="cancel-btn" @tap="closePopup">取消</text>
					<text class="title">选择省份</text>
					<text class="confirm-btn" @tap="confirmSelect">确定</text>
				</view>
				<scroll-view scroll-y class="picker-list">
					<view
						v-for="(item, index) in provinceList"
						:key="index"
						class="picker-list-item"
						:class="{'active': tempSelectedProvince === item}"
						@tap="selectTempProvince(item)">
						{{ item.label }}
					</view>
				</scroll-view>
			</view>
		</uni-popup>

		<!-- 城市选择弹窗 -->
		<uni-popup ref="cityPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="cancel-btn" @tap="closePopup">取消</text>
					<text class="title">选择城市</text>
					<text class="confirm-btn" @tap="confirmSelect">确定</text>
				</view>
				<scroll-view scroll-y class="picker-list">
					<view
						v-for="(item, index) in cityList"
						:key="index"
						class="picker-list-item"
						:class="{'active': tempSelectedCity === item}"
						@tap="selectTempCity(item)">
						{{ item.label }}
					</view>
				</scroll-view>
			</view>
		</uni-popup>

		<!-- 区域选择弹窗 -->
		<uni-popup ref="areaPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="cancel-btn" @tap="closePopup">取消</text>
					<text class="title">选择区域</text>
					<text class="confirm-btn" @tap="confirmSelect">确定</text>
				</view>
				<scroll-view scroll-y class="picker-list">
					<view
						v-for="(item, index) in areaList"
						:key="index"
						class="picker-list-item"
						:class="{'active': tempSelectedArea === item}"
						@tap="selectTempArea(item)">
						{{ item.label }}
					</view>
				</scroll-view>
			</view>
		</uni-popup>

		<!-- 考场选择弹窗 -->
		<uni-popup ref="examPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="cancel-btn" @tap="closePopup">取消</text>
					<text class="title">选择考场</text>
					<text class="confirm-btn" @tap="confirmSelect">确定</text>
				</view>
				<scroll-view scroll-y class="picker-list">
					<view
						v-for="(item, index) in examList"
						:key="index"
						class="picker-list-item"
						:class="{'active': tempSelectedExam === item}"
						@tap="selectTempExam(item)">
						{{ item.exam_name }}
					</view>
				</scroll-view>
			</view>
		</uni-popup>

		<!-- 考场选择弹窗 -->
		<uni-popup ref="buyMemberPopup" type="center" border-radius="10px 10px 10px 10px"  background-color="#B6C7EA">
			<view class="buyPopup">
				<view class="buyTitle">您正在购买</view>
				<view>
					<view>考场：{{ ExamInfo.exam_name }}（{{ cardMember.title }}）</view>
					<view>价格：{{ cardMember.truePrice || cardMember.price }}元</view>
					<view>手机：{{ phone }}</view>
				</view>
				<view class="tipText">
					请仔细核对信息，确认无误再购买。订单支付完成，不可修改不能退款。
				</view>
				<view class="buyBtnGroup">
					<button class="cancelBuy" @click="cancelBuy()">取消操作</button>
					<button class="submitBuy" @click="submitBuy()">确认支付</button>
				</view>
			</view>
		</uni-popup>

		<!-- 在最后一个uni-popup后添加支付成功弹窗 -->
		<uni-popup ref="paySuccessPopup" type="center" border-radius="10px 10px 10px 10px" background-color="#FFFFFF">
			<view class="paySuccessPopup">
				<view class="successIcon">
					<view class="iconfont icon-success">✓</view>
				</view>
				<view class="successTitle">支付成功</view>
				<view class="successPrice">￥{{ cardMember.truePrice || cardMember.price }}</view>
				<view class="successInfo">
					<view class="infoItem">
						<text class="label">订单编号：</text>
						<text class="value">{{ paySuccessInfo.orderNo || '' }}</text>
					</view>
					<view class="infoItem" style="margin-bottom: 0;">
						<text class="label">考场名称：</text>
						<text class="value">{{ paySuccessInfo.examName || '' }}</text>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { HTTP_REQUEST_URL } from '@/config/app.js'
import { getExamAreaList, getOpenIDApi } from '@/api/public.js';
import { getPhone2User, getExamPriceApi, createOrder, codeCreateUser } from '@/api/user.js';
import { checkPhone } from '@/utils/validate.js';
import {basicConfig} from '@/api/public';
import BatchAdd from './batchAdd.vue';
export default {
	components: {
		BatchAdd
	},

	data() {
		return {
			basicConfig: this.$Cache.get('BASIC_CONFIG'),
			apiURL: HTTP_REQUEST_URL,
			// 省市区数据
			province: {},
			city: {},
			area: {},
			exam: {},

			// 临时选择的值（确认前）
			tempSelectedProvince: {},
			tempSelectedCity: {},
			tempSelectedArea: {},
			tempSelectedExam: {},

			// 当前打开的选择器类型
			currentPickerType: '',

			// 三级数据（实际应用中应该从API获取）
			provinceList: [],
			cityList: [],
			areaList: [],
			examList: [],

			phone: this.$Cache.get('PHONE') || '',
			// 用户信息
			userInfo: {},
			cardMember: {},
			ExamInfo:{},
			openid: this.$Cache.get('OPENID') || 'ocCfH6ht6_mc3bZo3e47D5GtsuCQ',
			isOpenid: true,
			//openid: this.$Cache.get('OPENID') || '',
			code: '',
			paySuccessInfo: {
				amount: '',
				examName: '',
				orderNo: ''
			},
			isBuy: false,
			coachcode: this.$Cache.get('COACHCODE') || '',
			nickname: '',
			isNew: false,
			notUserText: '',
			isChangePhone: true,

			addtype: 1, // 1为单个，2为批量
		}
	},
	onLoad(e) {
		if(e.code){
			this.code = e.code;
		}
		this.getBasicConfig()
		// 初始化城市数据（实际应用中应该从API获取）
		this.initCityData();
	},
	onShow(){
		// this.getBasicConfig()
		// this.$nextTick(res=>{
		// 	if(typeof this.basicConfig === 'string'){
		// 		try {
		// 			this.basicConfig = JSON.parse(this.basicConfig);
		// 		} catch(err) {
		// 			console.error('解析 basicConfig 失败:', err);
		// 		}
		// 	}
		// 	if(!this.basicConfig){
		// 		this.getBasicConfig()
		// 	}
		// })
	},
	methods: {
		closeBatchAdd(){this.addtype = 1;},
		changeAddType(e){
			this.addtype = this.addtype == 1 ? 2 : 1;
		},
		changePhone(e){
			this.isChangePhone = true;
			this.userInfo = {uid: 0,phone: ''}
			this.cardMember = {}
		},
		bindCoach(){
			if(!checkPhone(this.phone)){
				return this.$util.Tips({title: '请输入学员手机号'});
			}
			if(this.coachcode.length < 6){
				return this.$util.Tips({title: '请输入6位优惠码'});
			}
			
			this.$Cache.set('COACHCODE',this.coachcode)
			codeCreateUser({phone: this.phone,coachcode: this.coachcode, nickname: this.nickname}).then(res=>{
				this.$util.Tips({title: '绑定成功'});
				this.isNew = false;
				this.getExamPrice();
			}).catch(err=>{
				this.$util.Tips({title: err});
			})
		},
		submitBuy(){
			if(!this.userInfo || !this.userInfo.uid || parseFloat(this.userInfo.uid) <= 0){
				console.log('this.userInfo',this.userInfo)
				return this.$util.Tips({title: '学员不存在'});
			}
			if(!this.cardMember || !this.cardMember.id || parseFloat(this.cardMember.id) <= 0){
				return this.$util.Tips({title: '会员卡不存在'});
			}
			if(!this.ExamInfo || !this.ExamInfo.id || parseFloat(this.ExamInfo.id) <= 0){
				return this.$util.Tips({title: '考场不存在'});
			}
			createOrder({uid: this.userInfo.uid, card_id: this.cardMember.id, exam_id: this.ExamInfo.id, openid: this.openid }).then(res=>{
				this.paySuccessInfo.orderNo = res.data.order_no;
				this.goPay(res.data);
			}).catch(err=>{
				this.$util.Tips({title: err});
			})

		},
		goPay(data) {
			// #ifdef H5
			// 添加微信环境检查
			if (!/MicroMessenger/i.test(navigator.userAgent)) {
				return this.$util.Tips({title: '请在微信浏览器中打开'});
			}

			// 添加支付参数检查
			if (!data || !data.pay_data) {
				return this.$util.Tips({title: '支付参数错误'});
			}

			// 详细打印支付参数，方便调试
			console.log('完整支付参数：', {
				appId: data.pay_data.appId,
				timeStamp: data.pay_data.timeStamp,
				nonceStr: data.pay_data.nonceStr,
				package: data.pay_data.package,
				signType: data.pay_data.signType,
				paySign: data.pay_data.paySign
			});

			if (typeof WeixinJSBridge == "undefined") {
				if (document.addEventListener) {
					document.addEventListener('WeixinJSBridgeReady', () => this.onBridgeReady(data.pay_data), false);
				} else if (document.attachEvent) {
					document.attachEvent('WeixinJSBridgeReady', () => this.onBridgeReady(data.pay_data));
					document.attachEvent('onWeixinJSBridgeReady', () => this.onBridgeReady(data.pay_data));
				}
			} else {
				this.onBridgeReady(data.pay_data);
			}
			// #endif
		},
		onBridgeReady(pay_data) {

			// 验证必要的支付参数
			const requiredParams = ['appId', 'timeStamp', 'nonceStr', 'package', 'paySign', 'signType'];
			const missingParams = requiredParams.filter(param => !pay_data[param]);

			if (missingParams.length > 0) {
				console.error('缺少支付参数：', missingParams);
				return this.$util.Tips({title: '支付参数不完整'});
			}

			// 继续支付流程
			WeixinJSBridge.invoke('getBrandWCPayRequest', {
				"appId": pay_data.appId,      // 注意大小写
				"timeStamp": pay_data.timeStamp,  // 注意大小写
				"nonceStr": pay_data.nonceStr,    // 注意大小写
				"package": pay_data.package,    // 完整的package字符串，包含prepay_id
				"signType": pay_data.signType,  // 使用后端返回的签名类型
				"paySign": pay_data.paySign     // 使用后端返回的签名
			}, (res) => {
				console.log('支付返回结果：', res);

				if (res.err_msg == "get_brand_wcpay_request:ok") {
					// 更新支付成功信息
					this.paySuccessInfo.examName= this.ExamInfo.exam_name;
					this.paySuccessInfo.amount  = this.cardMember.truePrice || this.cardMember.price;
					this.isBuy = true;

					// 关闭购买确认弹窗
					this.$refs.buyMemberPopup.close();

					// 显示支付成功弹窗
					this.$refs.paySuccessPopup.open();
				} else if (res.err_msg == "get_brand_wcpay_request:cancel") {
					this.$util.Tips({title: '取消支付'});
					this.$refs.buyMemberPopup.close();
				} else {
					this.$util.Tips({
						title: res.err_msg || '支付失败，请稍后重试'
					});
					console.error('支付失败详情：', res);
					this.$refs.buyMemberPopup.close();
				}
			});
		},
		cancelBuy(){this.$refs.buyMemberPopup.close();},
		getCode(){
			// #ifdef H5
			if(this.code == ''){
				if(!this.basicConfig || !this.basicConfig.appId){return uni.showToast({title:"ApiId不存在", icon: 'error'});}
				var REDIRECT_URI = encodeURIComponent(this.apiURL + '/membercard/')
				var Url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+(this.basicConfig.appId)+"&redirect_uri="+(REDIRECT_URI)+"&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect";
				window.location.href = Url;
			}else{
				this.getOpenID()
			}
			// #endif
		},
		getOpenID(){
			getOpenIDApi(this.code).then(res=>{
				this.openid = res.data.openid
				this.isOpenid = true
				this.$Cache.set('OPENID',this.openid)
			}).catch(err=>{
				uni.showToast({title: err, icon: 'none'})

				this.$Cache.set('BASIC_CONFIG','')
				this.basicConfig = {}
			})
		},
		getBasicConfig(){
			basicConfig().then(res=>{
				this.$Cache.set('BASIC_CONFIG',res.data)
				this.basicConfig = res.data

				console.log('this.openid',this.openid)
				if(!this.openid || this.openid == '' || !this.isOpenid){
					this.getCode();
				}
			})
		},
		buyMember(){
			this.$refs.buyMemberPopup.open();
		},
		getExamPrice(){
			this.cardMember = {};
			if(!checkPhone(this.phone)){
				uni.showToast({title: '请输入手机号',icon: 'none'})
				return
			}
			this.$Cache.set('PHONE',this.phone)
			if(!this.exam.id){
				uni.showToast({title: '请选择考场',icon: 'none'})
				return
			}
			uni.showLoading({title: '加载中...'})
			// 获取考试价格
			getExamPriceApi({phone: this.phone,exam_id: this.exam.id}).then(res=>{
				uni.hideLoading()
				this.isChangePhone = false;
				if(res.data.User){
					this.userInfo = res.data.User
					this.isNew = false;
				}else{
					this.userInfo = {uid: 0,phone: ''}
					this.isNew = true;
				}
				this.isBuy = res.data.isBuy || false;

				if(res.data.Member){
					this.cardMember = res.data.Member
					// 新增图片高宽比获取
					if (this.cardMember?.h5_price_image) {
						uni.getImageInfo({
							src: this.cardMember.h5_price_image,
							success: (res) => {
								// 修改为高度/宽度的比例
								const heightToWidthRatio = res.height / res.width;
								this.$set(this.cardMember, 'priceImageAspect', heightToWidthRatio);
								console.log('图片高宽比:', heightToWidthRatio);

								// 如果需要可以存储到缓存
								this.$Cache.set('PRICE_IMAGE_ASPECT', heightToWidthRatio);
							},
							fail: (err) => {
								console.error('获取图片信息失败:', err);
								this.$set(this.cardMember, 'priceImageAspect', 1); // 保持默认1:1比例
							}
						});
					}
				}else{
					this.cardMember = {}
				}

				if(res.data.Exam && res.data.Exam.id){
					this.ExamInfo = res.data.Exam;
				}else{
					this.ExamInfo = {}
				}

			}).catch(err=>{
				console.log('err.status',err.status)
				if(err.status == 60001){
					this.isChangePhone = false;
					this.userInfo = {uid: 0,phone: ''}
					this.isNew = true;
					this.notUserText = err.msg
					console.log('this.isNew',this.isNew)
				}
				uni.hideLoading()
				uni.showToast({
					title: err.msg || '请求失败',
					icon: err.msg.length > 10 ? 'none' : 'error'
				})
			})
		},
		// 初始化城市数据
		initCityData(city_id = 0, level='province') {
			this.cardMember = {};
			// 这里只是示例，实际应用中应该从API获取完整的省市区数据
			// 可以使用第三方库如 area-data
			getExamAreaList(city_id).then(res=>{
				if(level == 'province'){
					this.provinceList  = res.data.cityList || []
				}else if(level == 'city'){
					this.cityList  = res.data.cityList || []
					this.examList = res.data.ExamList && res.data.ExamList.list ? res.data.ExamList.list : []
				}else if(level == 'area'){
					this.areaList  = res.data.cityList || []
					this.examList = res.data.ExamList && res.data.ExamList.list ? res.data.ExamList.list : []
				}

				if(res.data.ExamList && res.data.ExamList.list){
					this.exam = {}
					this.examList = res.data.ExamList && res.data.ExamList.list ? res.data.ExamList.list : []
				}else{
					this.exam = {}
					this.examList = []
				}
			})
		},

		// 显示对应类型的选择器
		showPickerType(type) {
			this.currentPickerType = type;

			// 重置临时选择的值为当前已选值
			this.tempSelectedProvince = this.province;
			this.tempSelectedCity = this.city;
			this.tempSelectedArea = this.area;
			this.tempSelectedExam = this.exam;

			if (type === 'province') {
				this.$refs.provincePopup.open();
			} else if (type === 'city') {
				if (!this.province) {
					uni.showToast({
						title: '请先选择省份',
						icon: 'none'
					});
					return;
				}

				this.$refs.cityPopup.open();
			} else if (type === 'area') {
				if (!this.city) {
					uni.showToast({
						title: '请先选择城市',
						icon: 'none'
					});
					return;
				}

				this.$refs.areaPopup.open();
			} else if (type === 'exam') {
				if (!this.examList || !this.examList.length) {
					uni.showToast({
						title: '请先选择城市/区县',
						icon: 'none'
					});
					return;
				}

				this.$refs.examPopup.open();
			}
		},

		// 临时选择省份
		selectTempProvince(province) {
			this.tempSelectedProvince = province;
		},

		// 临时选择城市
		selectTempCity(city) {
			this.tempSelectedCity = city;
		},

		// 临时选择区域
		selectTempArea(area) {
			this.tempSelectedArea = area;
		},

		// 临时选择区域
		selectTempExam(exam) {
			this.tempSelectedExam = exam;
		},

		// 确认选择
		confirmSelect() {
			if (this.currentPickerType === 'province') {
				if (this.tempSelectedProvince !== this.province) {
					this.province = this.tempSelectedProvince;
					this.city = {};
					this.area = {};
					this.cityList = [];
					this.areaList = [];
					this.examList = [];
					this.initCityData(this.province.city_id, 'city')
				}
				this.$refs.provincePopup.close();
			} else if (this.currentPickerType === 'city') {
				if (this.tempSelectedCity !== this.city) {
					this.city = this.tempSelectedCity;
					this.area = {};
					this.areaList = [];
					this.examList = [];
					this.initCityData(this.city.city_id, 'area')
				}
				this.$refs.cityPopup.close();
			} else if (this.currentPickerType === 'area') {
				this.area = this.tempSelectedArea;
				this.$refs.areaPopup.close();
			} else if (this.currentPickerType === 'exam') {
				this.exam = this.tempSelectedExam;
				this.$refs.examPopup.close();
			}
		},

		// 关闭弹窗
		closePopup() {
			if (this.currentPickerType === 'province') {
				this.$refs.provincePopup.close();
			} else if (this.currentPickerType === 'city') {
				this.$refs.cityPopup.close();
			} else if (this.currentPickerType === 'area') {
				this.$refs.areaPopup.close();
			}
		},

		// 添加新的方法
		goToOrderList() {
			uni.redirectTo({
				url: '/pages/order/list'
			});
		},
	}
}
</script>

<style lang="scss">
.buyPopup{
	width: 90vw;padding: 40rpx 30rpx;font-size: 30rpx;
	.buyTitle{font-size: 33rpx;font-weight: bold;width: 100%;text-align: center;padding-bottom: 40rpx;}
	.tipText{width: 90%;padding-left: 5%;padding-top: 30rpx;color: #F00;text-align: center;}
	.buyBtnGroup{
		width:86%;padding-left:7%;display: flex;flex-direction: row;justify-content: space-around;padding-top:40rpx;
		.cancelBuy,.submitBuy{height: 80rpx;line-height: 72rpx;padding: 0 30rpx;font-size: 30rpx;color:#FFF;}
		.cancelBuy{background-color: #BBBBBB;border: 4rpx solid #777777;}
		.submitBuy{background-color: #4874CB;border: 4rpx solid #3861B1;}
	}
}
.bottomView{
	display: flex;flex-direction: row;justify-content: space-between;font-size: 22rpx;align-items: center;padding-bottom:0rpx;
	/*position: fixed;left:0;bottom: 30rpx;width:calc(100vw - 30rpx);*/
	image{width:30%;}
	view a{color:#CCC;}
}
.buyBtnView{
	display: flex;justify-content: center;align-items: center;padding-top:40rpx;margin-bottom:60rpx;
	button{
		display: flex;flex-direction:row;justify-content:center;align-items: center;padding-left: 30rpx;padding-right:10rpx;height:100rpx;line-height:100rpx;
		image{width: 70rpx;}
	}
}
.buyPrice{
	width: 100%;display: flex;flex-direction: column;justify-content: flex-end;padding-top:20rpx;font-size: 18rpx;
	.buyPriceItem{
		display: flex;flex-direction: row;justify-content: flex-end;align-items: center;padding-right:20rpx;padding-top:8rpx;
		&.lushe{color:rgb(2, 161, 2);}
	}
}
.BuyTips{
	width: 100%;display: flex;flex-direction: column;justify-content: flex-end;padding-top:20rpx;
	.buyTipItem{
		display: flex;flex-direction: row;justify-content: flex-end;align-items: center;padding-right:20rpx;
		image{width:14rpx;}
		text{font-size: 18rpx;color:#CCC;}
	}
}
.BuyViewTitle{font-size: 24rpx;width: 100%;text-align: center;color:#FFBD00;font-weight: bold;}
.buyView{width: 100%;min-height: 100rpx;background-size: 100% 100%;margin-top: 30rpx;}
.inputPhone{height: 70rpx; line-height: 78rpx;background-color: #FFF;border-radius: 10rpx;padding: 0 20rpx;color: #000;}
.buttonPhone{height: 70rpx; width: 150rpx; line-height: 70rpx;margin-left: 20rpx;}
page{color:#FFF;}
.pageView {
	width: 100vw;
	min-height: 100vh;
	background-size: 100% 100%;
	padding: 30rpx;
}

.PageLogo{
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	margin: 70rpx 0;
	.logo {width: 100rpx;}
	.appName{font-size: 45rpx;padding-left: 30rpx;font-weight: bold;color:#27acff;}
}

.cityView{
	display: flex;padding-top:20rpx;
	.CityLabel{width: 150rpx;padding-top: 16rpx;font-weight: bold;}
	.citySelectView{
		width: calc(100% - 150rpx);
		overflow: hidden;
		display: flex;
		flex-direction: column;
		.picker-container{
			display: flex;
			flex-direction: column;
			margin-bottom: 30rpx;
			.picker-item, .exam-site-item{
				background: none;
				width: calc(100% - 10rpx) !important;
				flex: 1;
				height: 70rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin: 0;
				margin-bottom: 20rpx;
				.pickerLineImg{height: 70rpx;}
				.pickerLineCenter{
					flex: 1;background-image: url("../../static/images/SelectC.png");height: 70rpx;background-size: 100% 100%;
					padding: 0 20rpx;color:#FFF;line-height: 70rpx;display: flex;flex-direction: row;justify-content: space-between;
					align-items: center;
					.picker-text{flex: 1;}
					.picker-arrow{margin-left: 10rpx;}
				}
				.picker-text {
					font-size: 28rpx;
					color: #FFF;
					flex: 1;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				.picker-arrow {
					font-size: 24rpx;
					color: #999;
					margin-left: 10rpx;
				}
			}
		}
	}
}


.title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	margin-top: 20rpx;
}

.citySelectView {
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}


.popup-content {
	background-color: #fff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
	overflow: hidden;
	min-height: 40vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 90rpx;
	padding: 0 30rpx;
	border-bottom: 1px solid #eee;
}

.cancel-btn, .confirm-btn {
	font-size: 30rpx;
	padding: 10rpx;
}

.cancel-btn {
	color: #999;
}

.confirm-btn {
	color: #1db0fc;
}

.title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.picker-list {
	max-height: 600rpx;
}

.picker-list-item {
	height: 90rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	color: #333;
	border-bottom: 1px solid #f5f5f5;

	&.active {
		color: #1db0fc;
		background-color: #f0f9ff;
	}
}

.no-data {
	padding: 40rpx 0;
	text-align: center;
	font-size: 28rpx;
	color: #999;
}

.paySuccessPopup {
	width: 600rpx;
	padding: 40rpx 30rpx;

	.successIcon {
		text-align: center;
		margin-bottom: 20rpx;

		.icon-success {
			display: inline-block;
			width: 100rpx;
			height: 100rpx;
			line-height: 100rpx;
			border-radius: 50%;
			background-color: #07c160;
			color: #fff;
			font-size: 50rpx;
			text-align: center;
		}
	}

	.successTitle {
		font-size: 32rpx;
		text-align: center;
		color: #333;
		margin-bottom: 30rpx;
		font-weight:100;
	}
	.successPrice{
		color: #000;
		font-size: 40px;
		font-weight: bold;
		text-align: center;
		padding-bottom: 20px;
	}

	.successInfo {
		background-color: #f8f8f8;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;

		.infoItem {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10rpx 0;
			font-size: 28rpx;

			.label {
				color: #666;
			}

			.value {
				color: #333;
				font-weight: 500;
			}
		}
	}

	.successBtns {
		display: flex;
		justify-content: space-between;
		gap: 20rpx;

		button {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			border-radius: 40rpx;

			&.viewOrder {
				background-color: #07c160;
				color: #fff;
			}

			&.backHome {
				background-color: #f8f8f8;
				color: #333;
			}
		}
	}
}
.notCantBuy{
	padding: 0;
    width: 50%;
    background-color: #4874CB;
    color: #FFF;
    font-size: 20px;
    border: 3px solid #2E54A1;
    border-radius: 8px;
}

.coachInfo {
	display: flex;
	align-items: center;
	padding: 0;
	border-radius: 12rpx;
	
	.coachAvatar {
		border: 1rpx solid #606060;
		width: 80rpx;
		height: 80rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
	}
	
	.coachNickname {
		font-size: 28rpx;
		color: #FFF;
		font-weight: 500;
	}
}
</style>
