// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from "@/utils/request.js";

/**
 * 创建订单
 */
export function createOrder(data){
	return request.post('order/create',data);
}

/**
 * 获取用户信息
 * 
 */
export function getUserInfo() {
	return request.get('userinfo');
}

/**
 * 通过手机号获取用户信息
 */
export function getPhone2User(phone){
	phone = phone.toString();
	if(phone.length != 11){return false;}
	return request.get('user/getPhone2User',{phone:phone});
}

/**
 * 获取考场会员价格
 */
export function getExamPriceApi(data){
	return request.post('exam/getExamPrice',data);
}

/**
 * 学员手机号绑定教练
 */
export function codeCreateUser(data){
	return request.get('user/coachcode_create_user',data);
}