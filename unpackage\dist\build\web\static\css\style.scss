	.index-wrapper .title{padding-top:20rpx;margin:0 20rpx;}
	.index-wrapper .title .text{font-size:24rpx;color:#999;}
	.index-wrapper .title .text .iconfont{font-size: 36rpx; color: var(--view-theme);margin-top: 4rpx;margin-right: 12rpx;  font-style: normal; font-weight: normal;}
	.index-wrapper .title .text .name{color:#282828;font-size:34rpx;font-weight:bold;margin-bottom:5rpx;position:relative;	display: flex;
			align-items: center;}
	.index-wrapper .title .text .name .new{position:absolute;top:2rpx;left:130rpx;font-size:16rpx;font-weight:bold;}
	.index-wrapper .title .more{font-size:24rpx;color:#999999!important;}
	.index-wrapper .title .more .iconfont{margin-left:9rpx;font-size:26rpx;}

.product-con .mask {
	z-index: 99!important;
}

.product-con .conter img {
	width: 750rpx!important;
	height: unset!important
}

.product-con .wrapper {
	background-color: #fff
}

.product-con .wrapper .share {
	margin: 0 30rpx;
	padding-top: 25rpx
}

.product-con .wrapper .share .money {
	font-size: 28rpx;
	font-weight: 700
}

.product-con .wrapper .share .money .num {
	font-size: 48rpx
}

.product-con .wrapper .share .money .vip-money {
	color: #282828;
	margin-left: 13rpx
}

.product-con .wrapper .share .money image {
	width: 46rpx;
	height: 21rpx;
	margin-left: 7rpx
}

.product-con .wrapper .share .money .vip-money {
	color: #282828;
	margin-left: 13rpx
}

.product-con .wrapper .share .iconfont {
	color: #515151;
	font-size: 40rpx;
	margin-bottom: 10rpx
}

.product-con .wrapper .introduce {
	font-size: 32rpx;
	font-weight: 700;
	margin: 23rpx 30rpx 0 30rpx;
	word-break: break-all;
}

.product-con .wrapper .label {
	margin: 0 30rpx;
	font-size: 24rpx;
	color: #82848f;
	margin-top: 22rpx
}

.product-con .wrapper .coupon {
	padding: 0 30rpx;
	border-top: 1rpx solid #f5f5f5;
	height: 80rpx;
	font-size: 26rpx;
	color: #82848f
}

.product-con .wrapper .coupon .hide {
	width: 540rpx;
	height: 80rpx;
	line-height: 80rpx
}

.product-con .wrapper .coupon .activity {
	height: 40rpx;
	padding: 0 20rpx;
	border: 1rpx solid var(--view-minorColor);
	color: var(--view-theme);
	font-size: 24rpx;
	line-height: 40rpx;
	position: relative;
	margin: 19rpx 0 19rpx 15rpx
}

.product-con .wrapper .coupon .activity:before {
	content: ' ';
	position: absolute;
	width: 7rpx;
	height: 10rpx;
	border-radius: 0 7rpx 7rpx 0;
	border: 1rpx solid var(--view-minorColor);
	background-color: #fff;
	bottom: 50%;
	left: -3rpx;
	margin-bottom: -6rpx;
	border-left-color: #fff
}

.product-con .wrapper .coupon .activity:after {
	content: ' ';
	position: absolute;
	width: 7rpx;
	height: 10rpx;
	border-radius: 7rpx 0 0 7rpx;
	border: 1rpx solid var(--view-minorColor);
	background-color: #fff;
	right: -3rpx;
	bottom: 50%;
	margin-bottom: -6rpx;
	border-right-color: #fff
}

.product-con .wrapper .coupon .iconfont {
	color: #7a7a7a;
	font-size: 28rpx
}

.product-con .attribute {
	background-color: #fff;
	padding: 0 30rpx;
	font-size: 26rpx;
	color: #82848f;
	margin-top: 20rpx;
	min-height: 80rpx
}

.product-con .attribute .atterTxt {
	font-size: 28rpx;
	color: #282828
}

.product-con .attribute .iconfont {
	font-size: 28rpx;
	color: #7a7a7a;
	line-height: 40rpx;
}

.product-con .userEvaluation {
	margin-top: 20rpx;
	background-color: #fff
}

.product-con .userEvaluation .title {
	height: 86rpx;
	border-bottom: 1rpx solid #eee;
	font-size: 28rpx;
	color: #282828;
	margin-left: 30rpx;
	padding-right: 30rpx
}

.product-con .userEvaluation .title .praise {
	font-size: 28rpx;
	color: grey
}

.product-con .userEvaluation .title .praise .iconfont {
	color: #7a7a7a;
	font-size: 28rpx;
	vertical-align: 1rpx;
	margin-left: 8rpx
}

.product-con .product-intro {
	position: relative;
	margin-top: 20rpx;
	width: 100%;
	padding-bottom: 100rpx;
	overflow: hidden;
}

.product-con .product-intro .title {
	font-size: 30rpx;
	color: #333;
	height: 86rpx;
	width: 100%;
	background-color: #fff;
	text-align: center;
	line-height: 86rpx
}

.product-con .product-intro .conter {
	width: 100%
}

.product-con .product-intro .conter image {
	width: 100%!important;
	display: block!important
}

.goodsStyle {
	margin-top: 1rpx;
	background-color: #fff;
	padding: 22rpx 30rpx;
	display: flex;
	align-items: center;
}

.goodsStyle .pictrue {
	width: 120rpx;
	height: 120rpx
}

.goodsStyle .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx
}

.goodsStyle .text {
	width: 545rpx;
	font-size: 28rpx;
	color: #999
}

.goodsStyle .text .name {
	width: 360rpx;
	height: max-content;
	color: #282828
}

.goodsStyle .text .money {
	text-align: right
}

.goodsStyle .text .money .num {
	margin-top: 7rpx
}

.goodWrapper .item {
	margin-left: 30rpx;
	margin-right: 30rpx;
	height: 180rpx;
	display: flex;
	align-items: center;
	flex-wrap: nowrap;
}

.goodWrapper .item .pictrue {
	width: 130rpx;
	height: 130rpx;
}

.goodWrapper .item .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx
}

.goodWrapper .item .text {
	width: calc(100% - 130rpx);
	padding-left: 20rpx;
	position: relative
}

.goodWrapper .item .text .name {
	font-size: 28rpx;
	color: #282828;
	width: 453rpx
}

.goodWrapper .item .text .num {
	font-size: 26rpx;
	color: #868686
}

.goodWrapper .item .text .attr {
	font-size: 20rpx;
	color: #868686;
	margin-top: 7rpx
}

.goodWrapper .item .text .money {
	font-size: 26rpx;
	margin-top: 17rpx
}

.goodWrapper .item .text .evaluate {
	position: absolute;
	width: 114rpx;
	height: 46rpx;
	border: 1rpx solid #bbb;
	border-radius: 4rpx;
	text-align: center;
	line-height: 46rpx;
	right: 0;
	bottom: -5rpx
}

.goodWrapper .item .text .evaluate.userEvaluated {
	font-size: 26rpx;
	color: #aaa;
	background-color: #f7f7f7;
	border-color: #f7f7f7
}

.promoterHeader {
	width: 100%;
	height: 220rpx
}

.promoterHeader .headerCon{width:100%;height:100%;padding:0 88rpx 0 55rpx;box-sizing:border-box;font-size:28rpx;color:#fff;background-image:url('data:image/png;base64,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');background-repeat:no-repeat;background-size:100% 100%;}
.promoterHeader .headerCon .name{margin-bottom:10rpx;}
.promoterHeader .headerCon .num{font-size:50rpx;}
.promoterHeader .headerCon .iconfont{font-size:125rpx;}
.sign-record .list .item .data{height:80rpx;line-height:80rpx;padding:0 30rpx;font-size:24rpx;color:#666;}
.sign-record .list .item .listn{background-color:#fff;font-size:24rpx;color:#999;}
.sign-record .list .item .listn .itemn .name{width:390rpx;font-size:28rpx;color:#282828;margin-bottom:10rpx;}
.sign-record .list .item .listn .itemn .num{font-size:36rpx;font-family: 'Guildford Pro';color:#16ac57;}
.sign-record .list .item .listn .itemn .num.font-color{color: var(--view-priceColor)!important;}
.coupon-list{padding:0 30rpx;margin-top:25rpx;}
.coupon-list .item{width:100%;height:170rpx;margin-bottom:16rpx;}
.coupon-list .item .money{width:220rpx;height:100%;color:#fff;font-size:36rpx;font-weight:bold;text-align:center;display: flex;flex-direction: column;align-items: center;justify-content: center;}
.coupon-list .item .moneyCon,.coupons .list .item .moneyCon{background-color:var(--view-theme);height: 100%;}
.coupon-list .item .money,.coupons .list .item .price{background-repeat:no-repeat;background-size:100% 100%;background-image: url('data:image/png;base64,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');}
.coupon-list .item .money.moneyGray,.coupons .list .item .price.moneyGray{background-color: #ccc;}
.coupon-list .item .money .num{font-size:60rpx;}
.coupon-list .item .text{width:470rpx;padding:6rpx 17rpx 0 24rpx;box-sizing:border-box;background-color:#fff;}
.coupon-list .item .text .condition{font-size:30rpx;color:#282828;height:93rpx;}
.coupon-list .item .text .data{font-size:20rpx;color:#999;height:50rpx;display: flex;flex-wrap: nowrap;}
.coupon-list .item .text .data .bnt{width:136rpx;height:44rpx;border-radius:22rpx;font-size:22rpx;text-align:center;line-height:44rpx;color:#fff;}
.coupon-list .item .text .data .bnt.gray{background:#ccc!important;}
.coupon-list .item.svip .money{background-color: #EDBB75;}
.coupon-list .item.svip .condition .line-title{border-color:#EEC181;background-color:#FEF7EC;color:#EEC181;}
.coupon-list .item.svip .bg-color{background: linear-gradient(90deg, #EDC98A 0%, #EDBB75 100%);}
.coupon-list .item.svip .bnt.gray{background:#CCCCCC;}
.coupon-list .item.svip .line1 .image{width:30rpx;height:30rpx;margin-right:10rpx;vertical-align:middle;z-index:0;}

.noCommodity {
	// border-top: 7rpx solid #f5f5f5
}

.noCommodity .pictrue {
	width: 414rpx;
	height: 336rpx;
	margin: 30rpx auto 30rpx auto
}

.noCommodity .pictrue image {
	width: 100%;
	height: 100%
}// 登录、注册、忘记密码
.register {
	background-image: linear-gradient(to bottom,#eb5447 0,#ff8e3b 100%);
	width: 100%;
	height: 100vh
}

.register .shading {
		background-image: url("data:image/png;base64,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");
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 100%;
		height: 286rpx;
		padding-top: 70rpx;
	}

.register .shading .pictrue {
	width: 172rpx;
	height: 172rpx;
	border-radius: 50%;
	background-color: rgba(255,255,255,.8);
	margin: 0 auto
}

.register .shading .pictrue image {
	width: 164rpx;
	height: 164rpx;
	border-radius: 50%;
	display: block
}

.register .whiteBg {
	width: 620rpx;
	border-radius: 16rpx;
	background-color: #fff;
	margin: 30rpx auto 0 auto;
	padding: 45rpx 30rpx 0 30rpx
}

.register .whiteBg .title {
	font-size: 36rpx;
	color: #282828;
	text-align: center;
	font-weight: 700
}

.register .whiteBg .title .item~.item {
	margin-left: 85rpx
}

.register .whiteBg .title .item {
	color: #999;
	border-bottom: 5rpx solid #fff;
	padding-bottom: 10rpx
}

.register .whiteBg .title .item.on {
	color: #282828;
	border-bottom-color: #f35749
}

.register .whiteBg .list .item {
	
	border-bottom: 1rpx solid #ededed;
	padding: 47rpx 0 13rpx 0;
	position: relative;
	width:100%;
	box-sizing: border-box;
}

.register .whiteBg .list .item .name {
	font-size: 26rpx;
	color: #2d3342;
	margin-bottom: 27rpx;
	text-align: left
}

.register .whiteBg .list .item image {
	width: 40rpx;
	height: 40rpx;
	display: block
}

.register .whiteBg .list .item input {
	font-size: 32rpx;
	flex: 1;
	margin-left: 20rpx
}

.register .whiteBg .list .item .placeholder {
	color: #ccc
}

.register .whiteBg .list .item .codeIput {
	width: 250rpx;
	margin-left: 20rpx
}

.register .whiteBg .list .item .code {
	position: absolute;
	width: 150rpx;
	height: 50rpx;
	background-color: #f35446;
	border-radius: 30rpx;
	color: #fff;
	line-height: 50rpx;
	text-align: center;
	bottom: 17rpx;
	right: 0;
	font-size: 25rpx
}

.register .whiteBg .list .item .code.on {
	background-color: #bbb
}

.register .whiteBg .list .forgetPwd {
	text-align: right;
	font-size: 28rpx;
	color: #666;
	margin-top: 20rpx
}

.register .whiteBg .list .forgetPwd .iconfont {
	font-size: 30rpx;
	margin-right: 10rpx;
	vertical-align: middle
}

.register .whiteBg .logon {
	font-size: 34rpx;
	color: #fff;
	font-weight: 700;
	height: 86rpx;
	border-radius: 43rpx;
	background: linear-gradient(to right,#f35447 0,#ff8e3c 100%);
	text-align: center;
	line-height: 86rpx;
	margin-top: 47rpx
}

.register .whiteBg .tip {
	height: 110rpx;
	text-align: center;
	line-height: 105rpx;
	font-size: 30rpx;
	color: #ccc
}

.register .bottom {
		background-image: url("data:image/png;base64,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");
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 620rpx;
		height: 36rpx;
		margin: 0 auto;
	}// 首页修改轮播内部样式；
.index-bg .uni-swiper-dot {
	width: 20rpx!important;
	height: 5rpx!important;
	border-radius: 3rpx
}

.boutique .uni-swiper-dot {
	width: 7rpx!important;
	height: 7rpx!important;
	border-radius: 50%
}

.boutique .uni-swiper-dot-active {
	width: 20rpx!important;
	border-radius: 5rpx!important
}

.sign-record .list .item .data {
	height: 80rpx;
	line-height: 80rpx;
	padding: 0 30rpx;
	font-size: 24rpx;
	color: #666
}

.sign-record .list .item .listn {
	background-color: #fff;
	font-size: 24rpx;
	color: #999
}

.sign-record .list .item .listn .itemn {
	// height: 120rpx;
	border-bottom: 1rpx solid #eee;
	padding: 30rpx;
}
.sign-record .list .item .listn .itemn .fail-msg {
	margin-top: 10rpx;
}

.sign-record .list .item .listn .itemn .name {
	// width: 390rpx;
	font-size: 28rpx;
	color: #282828;
	margin-bottom: 10rpx
}

.sign-record .list .item .listn .itemn .num {
	font-size: 36rpx;
	font-family: 'Guildford Pro';
	color: #16ac57
}

.statistical-page .mc-body {
	padding-bottom: 0
}

.statistical-page .mpvue-calendar {
	min-width: 100%
}

.statistical-page .mpvue-calendar table {
	margin: 0
}

.statistical-page .mpvue-calendar td {
	border-right: 1px solid #fff;
	padding: 0;
	width: 14%!important
}

.statistical-page .calendar-tools {
	box-shadow: unset;
	-webkit-box-shadow: unset;
	-o-box-shadow: unset;
	-moz-box-shadow: unset
}

.statistical-page .mc-head-box div {
	font-size: 14px
}

.statistical-page .mpvue-calendar td:not(.disabled) span.mc-date-red {
	color: unset
}

.statistical-page .mpvue-calendar .mc-range-mode .mc-range-begin span.calendar-date,.statistical-page .mpvue-calendar .mc-range-mode .mc-range-end span.calendar-date {
	border-radius: 0;
	background-color: #2291f8!important
}

.statistical-page .mpvue-calendar td.selected span.mc-date-red {
	color: #fff
}

.statistical-page .mc-range-mode .selected .mc-range-bg {
	background-color: #a0dcf9
}

.statistical-page .mpvue-calendar .mc-range-mode .mc-range-row-first .calendar-date,.statistical-page .mpvue-calendar .mc-range-mode .mc-range-row-last .calendar-date {
	background-color: #a0dcf9
}

.statistical-page .mpvue-calendar .mc-range-mode .selected.mc-range-second-to-last span {
	background-color: #a0dcf9
}

.statistical-page .mpvue-calendar .mc-range-mode .mc-range-month-first.selected .calendar-date,.statistical-page .mpvue-calendar .mc-range-mode .mc-range-month-last.selected .calendar-date {
	background-color: #a0dcf9
}

.statistical-page .mc-today-element .calendar-date {
	border-radius: 0;
	background-color: unset
}

.new-users .uni-swiper-dot {
	width: 8px;
	height: 4px;
	background: rgba(0,0,0,.15);
	border-radius: 2px
}

.new-users .uni-swiper-dot-active {
	width: 16px;
	height: 4px;
	background: var(--view-theme)!important;
	border-radius: 2px
}

.pictrue_log {
	width: 80rpx;
	height: 40rpx;
	border-radius: 20rpx 0 20rpx 0;
	line-height: 40rpx;
	font-size: 24rpx
}

.pictrue_log_class {
	// background: -webkit-gradient(linear,left top,right top,from(rgba(246,122,56,1)),to(rgba(241,27,9,1)));
	// background: linear-gradient(90deg,rgba(246,122,56,1) 0,rgba(241,27,9,1) 100%);
	background-color: var(--view-theme);
	opacity: 1;
	position: absolute;
	top: 0;
	left: 0;
	color: #fff;
	text-align: center;
	z-index: 3
}

.pictrue_log_medium {
	width: 80rpx;
	height: 44rpx;
	border-radius: 20rpx 0 20rpx 0;
	line-height: 44rpx;
	text-align: center;
	font-size: 26rpx
}

.pictrue_log_big {
	width: 100rpx;
	height: 46rpx;
	line-height: 46rpx;
	border-radius: 20rpx 0 20rpx 0;
	font-size: 28rpx
}

.spike-box .styleAll {
	background-color: #ffdfdd;
	color: #e93323;
	padding: 0 5rpx
}

.product-con .nav .time .timeTxt {
	color: #fff
}

.bg-color-hui {
	background: #bbb!important
}

.page_content .swiper .uni-swiper-dot {
	width: 20rpx!important;
	height: 5rpx!important;
	border-radius: 3rpx;
	background: rgba(0,0,0,.4)!important
}

.page_content .swiper .uni-swiper-dot-active {
	width: 20rpx!important;
	border-radius: 5rpx!important;
	background: #fff!important
}

.pictrue_log_xl {
	background: linear-gradient(90deg,rgba(246,122,56,1) 0,rgba(241,27,9,1) 100%)
}

.pictrue_log_xl_gray {
	background: linear-gradient(90deg,rgba(102,102,102,1) 0,rgba(153,153,153,1) 100%)
}

.pictrue_log_xl_blue {
	background: linear-gradient(90deg,rgba(26,163,246,1) 0,rgba(24,192,244,1) 100%)
}

.flex-aj-center {
	display: flex;
	align-items: center;
	justify-content: center
}
.page-index.bgf .noCommodity{
	border-top: 0;
}

.product-con .red{
	color: #82848f!important;
}

.borderShow{
	position: relative;
} 
.borderShow::after{
		content: ' ';
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		border:1px dashed #007AFF;
		box-sizing: border-box;
}	

uni-checkbox:not([disabled]) .uni-checkbox-input:hover{
	border-color: #d1d1d1;
}
.bg-green{
	background-color: #3CBB45;
}
.bg-theme{
	background-color: var(--view-theme);
}
uni-toast .uni-toast {
    font-size: 30rpx;
}

.status_bar{
 height: var(--status-bar-height);
}
.status_bar_red{
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: var(--status-bar-height);
	background: #e93323;
}
.eject{
	transform: translate3d(0, 0, 0) !important;
}

